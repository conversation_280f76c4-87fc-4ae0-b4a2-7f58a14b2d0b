using System;
using System.Diagnostics;
using Sirenix.OdinInspector;
using Infrastructure.Unity.Attributes.Odin;

namespace Infrastructure.Unity.Attributes
{
	[Conditional("UNITY_EDITOR")]
	[IncludeMyAttributes]
	[Minimum(0)]
	[SuffixLabel("@Infrastructure.Unity.Editor.TimeSuffixLabelEditorHelper.MillisecondToTimespan($property)", true)]
	public class TimeFromMsSuffixLabelAttribute : Attribute
	{
	}
}
