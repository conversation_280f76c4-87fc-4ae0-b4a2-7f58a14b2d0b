using System;
using System.Runtime.CompilerServices;
using UnityEngine;

namespace Logging
{
	using UnityObject = UnityEngine.Object;

	public abstract class BaseLogger<TLogContext> : Infrastructure.Shared.ILogger
	{
		protected abstract string prefix { get; }

		[HideInCallstack]
		public void Log(object msg, object context = null,
			[CallerMemberName] string memberName = "",
			[CallerLineNumber] int sourceLineNumber = 0)
		{
			DebugJournal.Log<TLogContext>($"{prefix} {msg}", (UnityObject) context, memberName, sourceLineNumber);
		}

		[HideInCallstack]
		public void LogWarning(object msg, object context = null,
			[CallerMemberName] string memberName = "",
			[CallerLineNumber] int sourceLineNumber = 0)
		{
			DebugJournal.LogWarning<TLogContext>($"{prefix} {msg}", (UnityObject) context, memberName, sourceLineNumber);
		}

		[HideInCallstack]
		public void LogError(object msg, object context = null,
			[CallerMemberName] string memberName = "",
			[CallerLineNumber] int sourceLineNumber = 0)
		{
			DebugJournal.LogError<TLogContext>($"{prefix} {msg}", (UnityObject) context, memberName, sourceLineNumber);
		}

		[HideInCallstack]
		public void LogException(Exception exception, object context = null,
			[CallerMemberName] string memberName = "",
			[CallerLineNumber] int sourceLineNumber = 0)
		{
			DebugJournal.LogException<TLogContext>(exception, null, (UnityObject) context, memberName, sourceLineNumber);
		}

		public NullReferenceException NullReferenceException(object msg) => new($"{prefix} {msg}");
		public Exception Exception(object msg) => new($"{prefix} {msg}");
	}
}