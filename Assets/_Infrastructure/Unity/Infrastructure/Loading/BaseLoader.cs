using System;
using System.Collections.Generic;
using System.Threading;
using Infrastructure.Shared.Extensions;
using Cysharp.Threading.Tasks;
using UnityEngine;
using VContainer.Unity;

namespace Loading
{
    public abstract class BaseLoader : IInitializable, IDisposable
    {
        private List<LoadingTask> _tasks;
        private CancellationTokenSource _cts = new();

        public event Action AllTasksCompleted;
        public event Action<LoadingTask> TaskCompleted;
        public event Action<LoadingTask, ILoadingTaskResult> TaskCompletedWithResult;
        public event Action<LoadingTask, float> TaskProgressChanged;
        public event Action<LoadingTask, LoadingTaskStatus> TaskStatusChanged;

        public virtual void Initialize()
        {
            InitializeTasks();
        }

        protected abstract List<LoadingTask> CreateTasks();

        private void InitializeTasks()
        {
            var tasks = CreateTasks();
            if (tasks.IsNullOrEmpty())
            {
                Debug.LogWarning($"Trying to initialize empty tasks for loader type {GetType()}");
                return;
            }

            _tasks = tasks;

            foreach (var task in tasks)
            {
                task.ProgressChanged += HandleTaskProgressChanged;
                task.StatusChanged += HandleTaskStatusChanged;
            }
        }

        public virtual void Dispose()
        {
            _cts?.Cancel();
            _cts?.Dispose();
            _cts = null;

            if (!_tasks.IsNullOrEmpty())
            {
                foreach (var task in _tasks)
                {
                    task.ProgressChanged -= HandleTaskProgressChanged;
                    task.StatusChanged -= HandleTaskStatusChanged;
                }
            }
        }

        public virtual async UniTask Load()
        {
            if (_tasks.IsNullOrEmpty())
            {
                Debug.LogWarning($"There is no loading tasks for loader type {GetType()}");
                return;
            }

            foreach (var task in _tasks)
            {
                if (task is ILoadingTaskWithResult taskWithResult)
                {
                    var result = await taskWithResult.LoadWithResult(_cts);
                    if (!_cts.IsCancellationRequested)
                    {
                        TaskCompleted?.Invoke(task);
                        TaskCompletedWithResult?.Invoke(task, result);
                    }

                    continue;
                }

                await task.Load(_cts);
                if (!_cts.IsCancellationRequested)
                {
                    TaskCompleted?.Invoke(task);
                }
            }
            
            AllTasksCompleted?.Invoke();
        }

        public float GetOverallProgress()
        {
            if (_tasks.IsNullOrEmpty())
                return 1f;

            var result = 0f;
            foreach (var task in _tasks)
            {
                result += task.Progress;
            }

            return result;
        }

        protected virtual void HandleTaskProgressChanged(LoadingTask task, float progress)
        {
            TaskProgressChanged?.Invoke(task, progress);
        }

        protected virtual void HandleTaskStatusChanged(LoadingTask task, LoadingTaskStatus status)
        {
            TaskStatusChanged?.Invoke(task, status);
        }
    }
}