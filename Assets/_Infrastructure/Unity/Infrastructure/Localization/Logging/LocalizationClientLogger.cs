using Localization;

namespace Logging.Localization
{
	public class InAppReviewClientLogger : BaseLogger<LocalizationDebug>
	{
		private static readonly string PREFIX = DebugJournal.GetChannelPrefix("Localization", LocalizationDebug.COLOR);

#if UNITY_EDITOR
		[UnityEditor.InitializeOnLoadMethod]
#else
		[RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.BeforeSplashScreen)]
#endif
		private static void Setup() => LocalizationDebug.logger ??= new InAppReviewClientLogger();

		protected override string prefix => PREFIX;
	}
}