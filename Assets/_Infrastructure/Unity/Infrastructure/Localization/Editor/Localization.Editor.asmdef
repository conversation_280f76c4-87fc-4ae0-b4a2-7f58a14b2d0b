{"name": "Localization.Editor", "rootNamespace": "", "references": ["Localization", "Unity.Mathematics", "Unity.Localization", "Unity.Localization.Editor", "Infrastructure.Unity", "Infrastructure.Unity.Editor", "Infrastructure.Shared", "Infrastructure.Shared.Mathematics"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}