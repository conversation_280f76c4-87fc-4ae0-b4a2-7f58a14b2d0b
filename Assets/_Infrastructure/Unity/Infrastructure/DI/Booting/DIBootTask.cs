using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using Sirenix.OdinInspector;
using UnityEngine;
using Object = UnityEngine.Object;

namespace Booting.DI
{
    [TypeRegistryItem(
        "\u2009DI", //В начале делаем отступ из-за отрисовки...
        "",
        SdfIconType.Code)]
    [Serializable]
    public class DIBootTask : BaseBootTask
    {
        [SerializeField] private GameObject _container;
        
        public override int Priority => HIGH_PRIORITY - 170;
        
        public override UniTask RunAsync(CancellationToken token = default)
        {
            return Object.InstantiateAsync(_container).ToUniTask(cancellationToken: token);
        }
    }
}