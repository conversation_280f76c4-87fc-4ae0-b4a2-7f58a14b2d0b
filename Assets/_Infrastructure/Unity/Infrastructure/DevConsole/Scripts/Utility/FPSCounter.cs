using System.Collections;
using TMPro;
using UnityEngine;

namespace DevConsole.UI
{
	public class FPSCounter : MonoBehaviour
	{
		private int _lastFrameCount;
		private float _lastTime;

		private void OnEnable()
		{
			StartCoroutine(FPSCount());
		}

		private IEnumerator FPSCount()
		{
			while (true)
			{
				_lastFrameCount = Time.frameCount;
				_lastTime = Time.realtimeSinceStartup;

				yield return new WaitForSecondsRealtime(_frequency);

				var timeSpan = Time.realtimeSinceStartup - _lastTime;
				var frameCount = Time.frameCount - _lastFrameCount;
				var fps = Mathf.RoundToInt(frameCount / timeSpan);

				_text.text = fps.ToString();
				_text.color = GetTextColor(fps);
			}
		}

		private Color GetTextColor(int fps)
		{
			if (fps >= 50) return Color.green;

			if (fps >= 30) return Color.yellow;

			return Color.red;
		}
#pragma warning disable 0649
		[SerializeField]
		private float _frequency = 0.5f;

		[SerializeField]
		private TextMeshProUGUI _text;
#pragma warning restore 0649
	}
}