using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using DevConsole.UI;
using UnityEngine;

namespace DevConsole
{
	public class PersistentDataViewBuilder : ViewBuilder
	{
		private const string ClassName = "PersistentData";
		private readonly ImageInspectorViewBuilder _imageInspectorViewBuilder = new();
		private readonly HashSet<FileInfo> _selectedFiles = new(new FileInfoComparer());
		private readonly TextInspectorViewBuilder _textInspectorViewBuilder = new();
		private string[] _filePaths;
		private bool _isSelectableMode;
		private NodeView _resultNode;

		public PersistentDataViewBuilder()
		{
			Node actionGroup = CreateCategories("Actions");
			AddButton("Refresh", "action", RefreshLocalFiles, actionGroup);

			Node allFilesGroup = CreateCategories("All Files", actionGroup);
			AddButton("Delete All Files", "delete", DeleteAllFiles, allFilesGroup);
			AddButton("Share All Files", "share", ShareAllFiles, allFilesGroup);

			var selectedGroup = CreateCategories("Selected", actionGroup);
			AddCheckbox("Selectable Mode", false, OnSelectableModeChanged, selectedGroup);
			AddButton("Delete Selected Files", "delete", DeleteSelectedFiles, selectedGroup);
			AddButton("Share Selected Files", "share", ShareSelectedFiles, selectedGroup);
		}

		public override void OnPrepareToShow()
		{
			base.OnPrepareToShow();
			RefreshLocalFiles(null);
		}

		private void RefreshLocalFiles(GenericNodeView nodeView)
		{
			if (_resultNode != null)
				_resultNode.RemoveFromParent();
			_resultNode = CreateCategory("Persistent Data");
			_resultNode.overrideDisplayText = "Persistent Data - " + DateTime.Now.ToString("[hh:mm:ss.ff]");

			_filePaths = Directory.GetFiles(Application.persistentDataPath, "*", SearchOption.AllDirectories);
			DrawHierarchy(_filePaths);
			CategoryPlayerPrefs.LoadCategoryStates(_rootNode, ClassName);

			Rebuild();
		}

		private void DrawHierarchy(string[] filePaths)
		{
			foreach (var path in filePaths)
			{
				var relativePath = path.Replace(Application.persistentDataPath, "").Trim(Path.DirectorySeparatorChar);
				var directoryPath = Path.GetDirectoryName(relativePath);

#if UNITY_EDITOR_WIN || UNITY_STANDALONE_WIN
				directoryPath = directoryPath.Replace(Path.DirectorySeparatorChar, Path.AltDirectorySeparatorChar);
#endif

				Node categoryNode = _resultNode;
				if (!string.IsNullOrEmpty(directoryPath))
					categoryNode = CreateCategories(directoryPath, _resultNode);

				var fileInfo = new FileInfo(path);
				var nodeName = string.Format("{0} <alpha=#66>({1:0.#} KB)", Path.GetFileName(path), fileInfo.Length / 1024.0f);
				if (_isSelectableMode)
				{
					var isOn = _selectedFiles.Contains(fileInfo);
					// if (_selectedFilePaths.TryGetValue(fileInfo, out var existedValue))
					// {
					// 	isOn = true;
					// 	fileInfo = existedValue;
					// }
					Node fileNode = AddCheckbox(nodeName, isOn, OnFileSelectChanged, categoryNode);
					fileNode.data = fileInfo;
				}
				else
				{
					Node fileNode = AddButton(nodeName, InspectFile, categoryNode);
					fileNode.data = fileInfo;
				}
			}
		}

		private void OnSelectableModeChanged(CheckboxNodeView node)
		{
			_isSelectableMode = node.isOn;
			RefreshLocalFiles(null);
		}

		private void OnFileSelectChanged(CheckboxNodeView node)
		{
			var fileInfo = (FileInfo) node.data;
			if (node.isOn)
				_selectedFiles.Add(fileInfo);
			else
				_selectedFiles.Remove(fileInfo);
		}

		private void InspectFile(GenericNodeView nodeView)
		{
			try
			{
				var fileInfo = (FileInfo) nodeView.data;
				if (fileInfo.Extension == ".png" || fileInfo.Extension == ".jpg")
				{
					_imageInspectorViewBuilder.SetContent(fileInfo);
					Console.PushSubView(_imageInspectorViewBuilder);
				}
				else
				{
					_textInspectorViewBuilder.SetContent(fileInfo);
					Console.PushSubView(_textInspectorViewBuilder);
				}
			}
			catch (Exception e)
			{
				Debug.LogError(e.Message);
			}
		}

		private void DeleteAllFiles(GenericNodeView nodeView)
		{
			if (_filePaths.Length == 0)
				return;

			var dir = new DirectoryInfo(Application.persistentDataPath);

			foreach (var file in dir.GetFiles()) file.Delete();

			foreach (var directory in dir.GetDirectories()) directory.Delete(true);

			RefreshLocalFiles(null);
		}

		private void DeleteSelectedFiles(GenericNodeView nodeView)
		{
			foreach (var fileInfo in _selectedFiles) fileInfo.Delete();

			_selectedFiles.Clear();
			RefreshLocalFiles(null);
		}

		private void ShareAllFiles(GenericNodeView nodeView)
		{
			if (_filePaths == null) _filePaths = Directory.GetFiles(Application.persistentDataPath, "*", SearchOption.AllDirectories);

			if (_filePaths.Length > 0) NativeShare.ShareMultiple("", _filePaths);
		}

		private void ShareSelectedFiles(GenericNodeView nodeView)
		{
			if (_selectedFiles.Count > 0)
			{
				var filePaths = _selectedFiles.Select(f => f.FullName).ToArray();
				NativeShare.ShareMultiple("", filePaths);
			}
		}

		protected override void OnCategoryToggled(GenericNodeView nodeView)
		{
			base.OnCategoryToggled(nodeView);
			CategoryPlayerPrefs.SaveCategoryState(nodeView, ClassName);
		}

		private class FileInfoComparer : IEqualityComparer<FileInfo>
		{
			public bool Equals(FileInfo x, FileInfo y)
			{
				if (x == null || y == null)
					return false;

				return StringComparer.Ordinal.Equals(x.FullName, y.FullName);
			}

			public int GetHashCode(FileInfo obj)
			{
				return StringComparer.Ordinal.GetHashCode(obj.FullName);
			}
		}
	}
}