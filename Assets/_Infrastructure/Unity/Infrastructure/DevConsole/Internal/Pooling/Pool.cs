using System.Collections.Generic;

namespace DevConsole
{
	public class Pool<T> where T : new()
	{
		private readonly List<T> _pools;

		public Pool(int capacity = 0)
		{
			_pools = new List<T>();

			PrecreateObject(capacity);
		}

		private void PrecreateObject(int capacity)
		{
			for (var i = 0; i < capacity; i++) _pools.Add(CreateNewObject());
		}

		public T Get()
		{
			if (_pools.Count > 0)
			{
				var obj = _pools[0];
				_pools.Remove(obj);

				return obj;
			}

			return CreateNewObject();
		}

		public void Return(T obj)
		{
			if (obj != null) _pools.Add(obj);
		}

		public void Return(List<T> listObj)
		{
			if (listObj != null) _pools.AddRange(listObj);
		}

		private T CreateNewObject()
		{
			return new T();
		}
	}
}