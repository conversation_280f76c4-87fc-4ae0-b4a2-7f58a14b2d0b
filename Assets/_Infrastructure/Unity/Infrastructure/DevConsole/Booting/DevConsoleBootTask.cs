#if DEBUG
#define DEV_CONSOLE
#endif
using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using Sirenix.OdinInspector;

#if DEV_CONSOLE
using Infrastructure.Unity.Reactive;
using DevConsole;
using InputManagement;
using UnityEngine;
using Console = DevConsole.Console;
#endif

namespace Booting.DevConsole
{
	[TypeRegistryItem(
		"\u2009Develop Console", //В начале делаем отступ из-за отрисовки...
		"",
		SdfIconType.Terminal)]
	[Serializable]
	public class DevConsoleBootTask : BaseBootTask
	{
		public override int Priority => HIGH_PRIORITY - 70;

#if DEV_CONSOLE
		private Console _console;

		[SerializeField]
		private int _touchCount = 2;

		[SerializeField, MinValue(0)]
		private float _swipeThreshold = 387f;

		private bool _isActive;
#else
		public override bool Active => false;
#endif

		public override async UniTask RunAsync(CancellationToken token = default)
		{
#if DEV_CONSOLE
			_console = await ConsoleLoader.LoadAsync();

			if (!_console)
				return;

			Console.OnVisibilityChanged += OnVisibilityChanged;
			InputReader.Swiped += OnSwiped;

			//Принудительно скрыть unity консоль (Development Console)!
			Debug.developerConsoleVisible = false;

			UnityLifecycle.UpdateEvent += OnUpdate;
#endif
		}

		protected override void OnDispose()
		{
#if DEV_CONSOLE
			ConsoleLoader.UnloadSafe();

			if (!_console)
				return;

			Console.OnVisibilityChanged -= OnVisibilityChanged;
			InputReader.Swiped -= OnSwiped;

			UnityLifecycle.UpdateEvent -= OnUpdate;
#endif
		}
#if DEV_CONSOLE
		private void OnSwiped(SwipeInfo info)
		{
			if (_isActive)
				return;

			if (info.touchCount != _touchCount)
				return;

			if (info.delta.sqrMagnitude <= _swipeThreshold * _swipeThreshold)
				return;

			if (info.ToDirection() != SwipeDirection.Down)
				return;

			ToggleLogView();
		}

		//TODO: перенести на InputManagement
		private void OnUpdate()
		{
			if (Input.GetKeyUp(KeyCode.BackQuote) || Input.GetKeyUp(KeyCode.Tilde))
			{
				if (_isActive)
				{
					_console.ToggleShowLogConsole();
					Console.CloseAllSubView();
				}
				else
				{
					if (Input.GetKey(KeyCode.LeftShift) || Input.GetKey(KeyCode.RightShift))
						ToggleLogView();
					else
					{
						ToggleCommandsView();
					}
				}
			}
		}

		public void ToggleLogView()
		{
			_console.ToggleShowLogConsole();
		}

		public void ToggleCommandsView()
		{
			if (!_isActive)
				_console.OpenCommandView();
			_console.ToggleShowLogConsole();
		}

		private void OnVisibilityChanged(bool isActive) => _isActive = isActive;
	}
#endif
	}