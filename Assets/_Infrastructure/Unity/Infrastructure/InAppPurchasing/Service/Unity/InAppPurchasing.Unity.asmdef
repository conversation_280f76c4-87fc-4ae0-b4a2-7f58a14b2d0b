{"name": "InAppPurchasing.Unity", "rootNamespace": "", "references": ["UniTask", "Unity.Services.Core", "InAppPurchasing", "UnityEngine.Purchasing", "UnityEngine.Purchasing.Stores", "UnityEngine.Purchasing.Security", "UnityEngine.Purchasing.Security.Generated", "UnityEngine.Purchasing.SecurityStub", "UnityEngine.Purchasing.SecurityCore", "Content", "Infrastructure.Shared", "Generic", "Game.App.LocalSave", "Infrastructure.Unity", "Distribution", "Infrastructure.Unity.Utility.UnityServices", "UserLocator", "Targeting", "LocalSave", "Infrastructure.Unity.LocalSave"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": ["IAP"], "versionDefines": [], "noEngineReferences": false}