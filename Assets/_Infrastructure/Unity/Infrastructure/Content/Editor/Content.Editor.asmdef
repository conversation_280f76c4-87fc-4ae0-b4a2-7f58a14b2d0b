{"name": "Content.Editor", "rootNamespace": "", "references": ["UniTask", "Content", "Content.Attributes", "Infrastructure.Shared", "Infrastructure.Unity", "Infrastructure.Unity.Editor"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}