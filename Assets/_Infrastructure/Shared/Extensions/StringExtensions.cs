using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using Infrastructure.Shared.Collections;
using Infrastructure.Shared.Pooling;

namespace Infrastructure.Shared.Extensions
{
	public static class StringExtensions
	{
		public const string NULL = "NULL";
		public const string EMPTY = "EMPTY";

		public static string Remove(this string str, string value)
		{
			return str.Replace(value, string.Empty);
		}

		public static bool IsNullOrEmpty([NotNullWhen(false)] this string? str)
		{
			return string.IsNullOrEmpty(str);
		}

		public static string Format(this string format, params object[] args)
		{
			return string.Format(format, args);
		}

		public static bool IsNullOrWhiteSpace(this string? str)
		{
			return string.IsNullOrWhiteSpace(str);
		}

		public static string GetCompositeString<T>(this IEnumerable<T>? collection, bool verticalOrHorizontal = true,
			Func<T, string> getter = null,
			bool numerate = true,
			string separator = "")
		{
			if (collection == null)
				return string.Empty;

			return GetCompositeString(new List<T>(collection), verticalOrHorizontal, getter, numerate, separator);
		}

		public static string GetCompositeString<T>(this List<T>? items, bool verticalOrHorizontal = true, Func<T, string> getter = null,
			bool numerate = true,
			string separator = "")
		{
			if (items == null)
				return NULL;
			if (items.Count == 0)
				return EMPTY;

			using (StringBuilderPool.Get(out var sb))
			{
				for (var i = 0; i < items.Count; i++)
				{
					var item = items[i];

					var value =
						item == null ? NULL :
						getter != null ? getter.Invoke(item) :
						item.ToString();

					var prefix = !separator.IsNullOrEmpty() ? i != 0 ? separator : string.Empty :
						numerate ? $"{i + 1}. " :
						null;

					var next = verticalOrHorizontal ? $"\n{prefix}{value}" : $"{prefix}{value}";

					sb.Append(next);
				}

				return sb.ToString();
			}
		}

		public static string GetCompositeString<T>(this Collection<T>? items, bool verticalOrHorizontal = true, Func<T, string> getter = null,
			bool numerate = true,
			string separator = "")
		{
			if (items == null)
				return NULL;

			if (items.Count == 0)
				return EMPTY;

			using (StringBuilderPool.Get(out var sb))
			{
				for (var i = 0; i < items.Count; i++)
				{
					var item = items[i];

					var value =
						item == null ? NULL :
						getter != null ? getter.Invoke(item) :
						item.ToString();

					var prefix = !separator.IsNullOrEmpty() ? i != 0 ? separator : string.Empty :
						numerate ? $"{i + 1}. " :
						null;

					var next = verticalOrHorizontal ? $"\n{prefix}{value}" : $"{prefix}{value}";

					sb.Append(next);
				}

				return sb.ToString();
			}
		}
	}
}