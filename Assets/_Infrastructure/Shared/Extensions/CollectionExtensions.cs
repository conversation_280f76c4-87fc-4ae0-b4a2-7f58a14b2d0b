using System;
using System.Collections.Generic;
using System.Linq;
using Infrastructure.Shared.Collections;

namespace Infrastructure.Shared.Extensions
{
	public static class CollectionExtensions
	{
		public static bool IsNullOrEmpty<T>(this Collection<T>? collection)
		{
			return collection == null || IsEmpty(collection);
		}

		public static bool IsNullOrEmpty<T>(this ICollection<T>? collection)
		{
			return collection == null || IsEmpty(collection);
		}

		public static bool IsEmpty<T>(this Collection<T> collection)
		{
			return collection.Count == 0;
		}

		public static bool IsEmpty<T>(this ICollection<T> collection)
		{
			return collection.Count == 0;
		}

		public static bool Any<T>(this Collection<T> collection)
			=> !collection.IsNullOrEmpty();

		public static bool Any<T>(this ICollection<T> collection)
			=> !collection.IsNullOrEmpty();

		public static bool IsNullOrEmpty<T>(this IEnumerable<T>? enumerable)
		{
			return enumerable == null || IsEmpty(enumerable);
		}

		public static bool ReferenceContains<T>(this IList<T> collection, T item)
		{
			if (!Any(collection))
				return false;

			// ReSharper disable once ForCanBeConvertedToForeach
			// ReSharper disable once LoopCanBeConvertedToQuery
			for (var i = 0; i < collection.Count; i++)
			{
				if (ReferenceEquals(collection[i], item))
					return true;
			}

			return false;
		}

		public static bool ReferenceContains<T>(this IEnumerable<T> collection, T target)
		{
			foreach (var item in collection)
			{
				if (ReferenceEquals(item, target))
					return true;
			}

			return false;
		}

		public static bool Any<T>(this IList<T> collection, Func<T, bool> predicate)
		{
			if (!Any(collection))
				return false;

			// ReSharper disable once ForCanBeConvertedToForeach
			// ReSharper disable once LoopCanBeConvertedToQuery
			//TODO:CollectionsMarshal.AsSpan() в .NET 5+
			for (var i = 0; i < collection.Count; i++)
			{
				if (predicate(collection[i]))
					return true;
			}

			return false;
		}

		public static bool All<T>(this IList<T> collection, Func<T, bool> predicate)
		{
			// ReSharper disable once ForCanBeConvertedToForeach
			// ReSharper disable once LoopCanBeConvertedToQuery

			//TODO:CollectionsMarshal.AsSpan() в .NET 5+
			for (var i = 0; i < collection.Count; i++)
			{
				if (!predicate(collection[i]))
					return false;
			}

			return true;
		}

		public static void Shuffle<T>(this List<T> list, IRandomizer<int> randomizer)
		{
			var length = list.Count;
			for (var i = 0; i < length; i++)
			{
				var indexToSwap = randomizer.Next(i, length);
				(list[i], list[indexToSwap]) = (list[indexToSwap], list[i]);
			}
		}

		public static bool Any<T>(this T[] array, Predicate<T> predicate)
		{
			if (array == null)
				throw new ArgumentNullException(nameof(array));

			// ReSharper disable once ForCanBeConvertedToForeach
			// ReSharper disable once LoopCanBeConvertedToQuery
			for (var i = 0; i < array.Length; i++)
			{
				if (predicate(in array[i]))
					return true;
			}

			return false;
		}

		public static bool All<T>(this T[] array, Predicate<T> predicate)
		{
			if (array == null)
				throw new ArgumentNullException(nameof(array));

			// ReSharper disable once ForCanBeConvertedToForeach
			// ReSharper disable once LoopCanBeConvertedToQuery
			for (var i = 0; i < array.Length; i++)
			{
				if (!predicate(in array[i]))
					return false;
			}

			return true;
		}

		public static bool AnySafe<T>(this T[] array, Predicate<T> predicate)
		{
			if (array == null)
				return false;

			// ReSharper disable once ForCanBeConvertedToForeach
			// ReSharper disable once LoopCanBeConvertedToQuery
			for (var i = 0; i < array.Length; i++)
			{
				if (predicate(in array[i]))
					return true;
			}

			return false;
		}

		public static bool AllSafe<T>(this T[] array, Predicate<T> predicate)
		{
			if (array == null)
				return false;

			// ReSharper disable once ForCanBeConvertedToForeach
			// ReSharper disable once LoopCanBeConvertedToQuery
			for (var i = 0; i < array.Length; i++)
			{
				if (!predicate(in array[i]))
					return false;
			}

			return true;
		}

		public static bool ContainsIndex<T>(this T[] array, int index)
		{
			return index >= 0 && index < array.Length;
		}

		public static bool ContainsIndexSafe<T>(this T[] array, int index)
		{
			if (array == null)
				return false;

			return index >= 0 && index < array.Length;
		}

		public static bool ContainsIndexSafe<T>(this IList<T>? list, int index)
		{
			if (list == null)
				return false;

			return index >= 0 && index < list.Count;
		}

		public static T First<T>(this IList<T> list) => list[0];

		public static T FirstOrDefault<T>(this IList<T>? list, T defaultValue = default)
			=> ElementAtOrDefault(list, 0, defaultValue);

		public static T Second<T>(this IList<T> list) => list[1];

		public static T SecondOrDefault<T>(this IList<T>? list, T defaultValue = default)
			=> ElementAtOrDefault(list, 1, defaultValue);

		public static T Third<T>(this IList<T> list) => list[2];

		public static T ElementAtOrDefault<T>(this IList<T>? list, int index, T defaultValue = default)
		{
			if (list == null || list.Count - 1 < index)
				return defaultValue;

			return list[index];
		}

		public static T ThirdOrDefault<T>(this IList<T>? list, T defaultValue = default)
			=> ElementAtOrDefault(list, 2, defaultValue);

		public static T Last<T>(this IList<T> list) => list[^1];

		public static T LastOrDefault<T>(this IList<T> list, T defaultValue = default)
		{
			if (IsNullOrEmpty(list))
				return defaultValue;

			return Last(list);
		}

		public static ref T First<T>(this Collection<T> list) => ref list[0];

		public static T FirstOrDefault<T>(this Collection<T>? list, in T defaultValue = default)
			=> ElementAtOrDefault(list, 0, in defaultValue);

		public static ref T Second<T>(this Collection<T> list) => ref list[1];

		public static T SecondOrDefault<T>(this Collection<T>? list, in T defaultValue = default)
			=> ElementAtOrDefault(list, 1, defaultValue);

		public static ref T Third<T>(this Collection<T> list) => ref list[2];

		public static T ElementAtOrDefault<T>(this Collection<T>? list, int index, in T defaultValue = default)
		{
			if (list == null || list.Count - 1 < index)
				return defaultValue;

			return list[index];
		}

		public static T ThirdOrDefault<T>(this Collection<T>? list, in T defaultValue = default)
			=> ElementAtOrDefault(list, 2, in defaultValue);

		public static ref T Last<T>(this Collection<T> list) => ref list[^1];

		public static T LastOrDefault<T>(this Collection<T> list, in T defaultValue = default)
		{
			if (IsNullOrEmpty(list))
				return defaultValue;

			return Last(list);
		}

		public static bool IsEmpty<T>(this IEnumerable<T> enumerable) => !enumerable.Any();

		public static List<T> AddRangeRepeated<T>(this List<T> list, T repeatedItem, int count)
		{
			list.AddRange(Enumerable.Repeat(repeatedItem, count));
			return list;
		}

		public static IEnumerable<(T, int)> WithIndex<T>(this IList<T> list)
		{
			if (list == null)
				throw new ArgumentException("List is null", nameof(list));

			for (int i = 0; i < list.Count; i++)
				yield return (list[i], i);
		}

		public static IEnumerable<(T, int)> WithIndexSafe<T>(this IList<T> list)
		{
			if (list == null)
				yield break;

			for (int i = 0; i < list.Count; i++)
				yield return (list[i], i);
		}

		public static IEnumerable<(T, int)> WithIndexSafe<T>(this IEnumerable<T> enumerable)
		{
			if (enumerable == null)
				yield break;

			var index = 0;

			foreach (var value in enumerable)
				yield return (value, index++);
		}

		public static IEnumerable<(T, int)> WithIndex<T>(this IEnumerable<T> enumerable)
		{
			if (enumerable == null)
				throw new ArgumentException("Enumerable is null", nameof(enumerable));

			var index = 0;

			foreach (var value in enumerable)
				yield return (value, index++);
		}

		public delegate bool Predicate<T>(in T value);
	}
}