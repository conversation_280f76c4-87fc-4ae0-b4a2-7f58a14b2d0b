using System;
using Infrastructure.Shared.Utility;

namespace Infrastructure.Shared.Data
{
#if CLIENT
	public interface IEnumMask {}
#endif

	[Serializable]
	public struct EnumMask<T>
#if CLIENT
		: IEnumMask
#endif
		where T : unmanaged, Enum
	{
		public static readonly EnumMask<T> All = new() { mask = ~0 };

		public int mask;

		public static EnumMask<T> Create(T value)
		{
			var result = new EnumMask<T>();
			result.Add(value);
			return result;
		}

		public bool HasNothing()
			=> mask == 0;

		public readonly bool Has(T value)
			=> Has(value.ToInt());

		public readonly bool HasOnly(T value)
			=> HasOnly(value.ToInt());

		public readonly bool Has(int value)
			=> (mask & (1 << value)) != 0;

		public readonly bool HasOnly(int value)
		{
			var valueMask = 1 << value;
			return ((mask & (1 << value)) != 0) && ((mask & ~valueMask) == 0);
		}

		public readonly bool Has(EnumMask<T> value)
			=> (value.mask & mask) != 0;

		public void Add(T value)
		{
			Add(value.ToInt());
		}

		public void Add(int value) => mask |= (1 << value);

		public void Remove(T value) => Remove(value.ToInt());

		public void Remove(int value) => mask &= ~(1 << value);

		public static EnumMask<T> operator |(EnumMask<T> a, EnumMask<T> b) => new() { mask = a.mask | b.mask };

		public static EnumMask<T> operator &(EnumMask<T> a, EnumMask<T> b) => new() { mask = a.mask & b.mask };

		public static implicit operator EnumMask<T>(T value)
		{
			var mask = new EnumMask<T>();
			mask.Add(value);
			return mask;
		}

		public static implicit operator uint(EnumMask<T> value) => value.mask.As<int, uint>();

		public static implicit operator EnumMask<T>(uint value)
		{
			var mask = value.As<uint, int>();
			return new EnumMask<T>
			{
				mask = mask,
			};
		}
	}

	public static class EnumMask
	{
		public static EnumMask<T> From<T>(params T[] values)
			where T : unmanaged, Enum
		{
			var mask = new EnumMask<T>();

			for (int i = 0; i < values.Length; i++)
				mask.Add(values[i]);

			return mask;
		}
	}
}
