namespace Infrastructure.Shared.Collections
{
	public struct ArrayReference<T>
	{
		private T[] _array;
		private int _index;

		public ArrayReference(T[] array, int index)
		{
			_array = array;
			_index = index;
		}

		public ref readonly T Value => ref _array[_index];

		public bool IsEmpty => _array is not {Length: > 0};

		public static implicit operator T(in ArrayReference<T> reference) => reference.Value;
	}

	/// <summary>
	/// Есть ArraySegment, но у него доступ по индексу не ref!
	/// </summary>
	public struct ArraySection<T>
	{
		private T[] _array;

		private int _start;
		private int _end;

		public ArraySection(T[] array, int start, int end)
		{
			_array = array;

			_start = start;
			_end = end;
		}

		public ref readonly T this[int index] => ref _array[index];

		public bool Contains(int index)
		{
			if (index < _start)
				return false;

			if (index > _end)
				return false;

			return true;
		}
		public Enumerator GetEnumerator() => new(_array, _start, _end);

		public ref struct Enumerator
		{
			private readonly T[] _array;
			private int _index;
			private int _end;

			public ref T Current => ref _array[_index];

			public Enumerator(T[] array, int start, int end)
			{
				_array = array;

				_index = start;
				_end = end;
			}

			public bool MoveNext()
			{
				_index++;
				return _index < _end;
			}
		}
	}
}