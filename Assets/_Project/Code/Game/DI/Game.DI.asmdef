{"name": "Game.DI", "rootNamespace": "", "references": ["Infrastructure.Shared", "<PERSON><PERSON><PERSON>", "Booting", "Presenters", "Infrastructure.Unity", "Game.Loaders", "Game.Presenters", "Game.DI.Infrastructure", "Game.Core"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}