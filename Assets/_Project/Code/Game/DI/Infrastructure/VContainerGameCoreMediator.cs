using System;
using System.Collections.Concurrent;
using System.Reflection;
using System.Threading.Tasks;
using Game.Core.Abstractions.Messaging;
using VContainer;

namespace Game.DI.Infrastructure
{
    /// <summary>
    /// Медиатор для обработки команд и запросов к ядру игры.
    /// </summary>
    ///
    /// <remarks>
    /// Разрешает зависимости обработчиков через VContainer.
    /// Использует рефлексию для вызова метода Handle(...) обработчика.
    /// </remarks>
    public class VContainerGameCoreMediator : IGameCoreMediator
    {
        // Кэш для хранения методов Handle(...) обработчиков
        // Необходимо для уменьшения количества рефлексии - операций поиска метода Handle(...) при каждом вызове
        private static readonly ConcurrentDictionary<Type, MethodInfo> HandleCache = new();
        
        private readonly IObjectResolver _resolver;
        
        public VContainerGameCoreMediator(IObjectResolver resolver) => _resolver = resolver;

        /// <inheritdoc cref="IGameCoreMediator.ExecuteCommand{TResult}"/>
        public async Task<TResult> ExecuteCommand<TResult>(ICommand<TResult> command)
            where TResult : IResult
        {
            var handlerType = typeof(ICommandHandler<,>).MakeGenericType(command.GetType(), typeof(TResult));
            
            return await Handle<TResult>(handlerType, command);
        }

        /// <inheritdoc cref="IGameCoreMediator.ExecuteCommand{TResult}"/>
        public async Task<TResult> ExecuteQuery<TResult>(IQuery<TResult> query)
            where TResult : IResult
        {
            var handlerType = typeof(IQueryHandler<,>).MakeGenericType(query.GetType(), typeof(TResult));
            
            return await Handle<TResult>(handlerType, query);
        }
        
        private async Task<TResult> Handle<TResult>(Type handlerType, object command)
        {
            var handler= _resolver.Resolve(handlerType);
            
            var method = GetHandleMethod(handlerType);

            var task = (Task<TResult>)method.Invoke(handler, new object[] { command });
            
            return await task.ConfigureAwait(false);
        }

        private static MethodInfo GetHandleMethod(Type handlerType) =>
            HandleCache.GetOrAdd(handlerType, t =>
                t.GetMethod("Handle", BindingFlags.Instance | BindingFlags.Public)
                ?? throw new InvalidOperationException($"Handler {t} must have public Handle(...)"));
    }
}
