using System.Threading.Tasks;

namespace Game.Core.Abstractions.Messaging
{
    /// <summary>
    /// Контракт медиатора для обработки команд и запросов в паттерне CQRS.
    /// </summary>
    ///
    /// <remarks>
    /// Медиатор инкапсулирует логику поиска и вызова соответствующего обработчика для команды или запроса к ядру игровой логики.
    /// </remarks>
    public interface IGameCoreMediator
    {
        /// <summary>
        /// Выполняет указанную команду.
        /// </summary>
        ///
        /// <remarks>
        /// Команды изменяют состояние системы.
        /// </remarks>
        /// 
        /// <param name="command">Команда, которую необходимо выполнить.</param>
        /// 
        /// <returns>Результат выполнения команды.</returns>
        public Task<TResult> ExecuteCommand<TResult>(ICommand<TResult> command)
            where TResult : IResult;
         
        /// <summary>
        /// Выполняет указанный запрос.
        /// </summary>
        /// 
        /// <remarks>
        /// Запросы не изменяют состояние системы.
        /// </remarks>
        /// 
        /// <param name="query">Запрос, который необходимо выполнить.</param>
        /// 
        /// <returns>Результат выполнения запроса.</returns>
        public Task<TResult> ExecuteQuery<TResult>(IQuery<TResult> query)
            where TResult : IResult;
    }
}
