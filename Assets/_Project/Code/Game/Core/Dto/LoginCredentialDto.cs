using Game.Core.Models.Authentication;

namespace Game.Core.Dto
{
    /// <summary>
    /// Данные для аутентификации на сервере.
    /// </summary>
    ///
    /// <remarks>
    /// Является "точкой входа" в аккаунт пользователя.
    /// </remarks>
    public class LoginCredentialDto
    {
        /// <summary>
        /// Метода аутентификации.
        /// </summary>
        public AuthMethod AuthMethod { get; set; }
        
        /// <summary>
        /// Токен аутентификации.
        /// </summary>
        public string AuthToken { get; set; }
    }
}
