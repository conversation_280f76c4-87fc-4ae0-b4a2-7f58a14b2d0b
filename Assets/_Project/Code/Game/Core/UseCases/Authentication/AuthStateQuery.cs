using Game.Core.Abstractions.Messaging;

namespace Game.Core.UseCases.Authentication
{
    /// <summary>
    /// Запрос для получения состояния аутентификации пользователя.
    /// </summary>
    ///
    /// <remarks>
    /// Проверяет, наличие токена аутентификации и рефреш-токена в локальном хранилище.
    /// Также проверяет срок действия токенов.
    /// </remarks>
    public class AuthStateQuery : IQuery<AuthStateQueryResult> { }
}
