using Game.Core.Abstractions.Messaging;
using Game.Core.Dto;

namespace Game.Core.UseCases.Authentication
{
    public class PlatformLoginCredentialsQueryResult : IResult
    {
        /// <inheritdoc cref="IResult.IsResultSuccess"/>
        public bool IsResultSuccess { get; set; }
        
        /// <inheritdoc cref="IResult.ResultMessage"/>
        public string ResultMessage { get; set; }
        
        /// <summary>
        /// Список доступных для входа способов аутентификации.
        /// Всегда содержит гостевую аутентификацию.
        /// Так же может содержать аутентификацию через GooglePlayGames или AppleGameCenter.
        /// </summary>
        public LoginCredentialDto[] LoginCredentials { get; set; }
    }
}
