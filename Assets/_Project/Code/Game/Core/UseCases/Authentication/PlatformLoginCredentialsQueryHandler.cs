using System.Threading.Tasks;
using Game.Core.Abstractions.Messaging;
using Game.Core.Dto;
using Game.Core.Models.Authentication;

namespace Game.Core.UseCases.Authentication
{
    public class PlatformLoginCredentialsQueryHandler : IQueryHandler<PlatformLoginCredentialsQuery, PlatformLoginCredentialsQueryResult>
    {
        public Task<PlatformLoginCredentialsQueryResult> Handle(PlatformLoginCredentialsQuery query)
        {
            return Task.FromResult(new PlatformLoginCredentialsQueryResult
            {
                IsResultSuccess = true,
                LoginCredentials = new[]
                {
                    new LoginCredentialDto
                    {
                        AuthMethod = AuthMethod.Guest,
                        AuthToken = "GuestToken"
                    }
                }
            });
        }
    }
}
