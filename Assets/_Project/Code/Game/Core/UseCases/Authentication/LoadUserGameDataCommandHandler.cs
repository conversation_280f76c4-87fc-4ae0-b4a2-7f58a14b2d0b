using System.Threading.Tasks;
using Game.Core.Abstractions.Messaging;

namespace Game.Core.UseCases.Authentication
{
    public class LoadUserGameDataCommandHandler : ICommandHandler<LoadUserGameDataCommand, LoadUserGameDataCommandResult>
    {
        public Task<LoadUserGameDataCommandResult> Handle(LoadUserGameDataCommand command)
        {
            return Task.FromResult(new LoadUserGameDataCommandResult
            {
                IsResultSuccess = true,
            });
        }
    }
}
