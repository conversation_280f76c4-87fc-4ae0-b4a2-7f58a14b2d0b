using System.Threading.Tasks;
using Game.Core.Abstractions.Messaging;

namespace Game.Core.UseCases.Authentication
{
    public class AuthStateQueryHandler : IQueryHandler<AuthStateQuery, AuthStateQueryResult>
    {
        public Task<AuthStateQueryResult> Handle(AuthStateQuery query)
        {
            return Task.FromResult(new AuthStateQueryResult
            {
                IsResultSuccess = true,
                
                IsUserAuthenticated = false
            });
        }
    }
}
