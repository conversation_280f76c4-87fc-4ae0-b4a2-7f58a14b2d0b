using Game.Core.Abstractions.Messaging;

namespace Game.Core.UseCases.Authentication
{
    public class AuthStateQueryResult : IResult
    {
        /// <inheritdoc cref="IResult.IsResultSuccess"/>
        public bool IsResultSuccess { get; set; }
        
        /// <inheritdoc cref="IResult.ResultMessage"/>
        public string ResultMessage { get; set; }
        
        /// <summary>
        /// Флаг аутентификации пользователя.
        /// </summary>
        ///
        /// <remarks>
        /// <list type="bullet">
        /// <item><description>
        /// True - пользователь аутентифицирован.
        /// </description></item>
        /// <item><description>
        /// False - пользователь не аутентифицирован.
        /// Для аутентификации необходимо выполнить команду <see cref="LoginCommand"/>.
        /// </description></item>
        /// </list>
        /// </remarks>
        public bool IsUserAuthenticated { get; set; }
    }
}
