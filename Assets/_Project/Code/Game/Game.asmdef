{"name": "Game", "rootNamespace": "", "references": ["Infrastructure.Shared", "<PERSON><PERSON><PERSON>", "Content.ScriptableObjects", "Content", "Content.ScriptableObjects.UI", "Unity.Addressables", "AssetManagement", "UI.Windows"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}