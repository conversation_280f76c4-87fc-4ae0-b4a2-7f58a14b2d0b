using VContainer;
using VContainer.Unity;
using Game.Core.Abstractions.Messaging;
using Game.Core.UseCases.Authentication;
using Game.Loaders.Content;
using Game.Presenters;
using Game.Presenters.Infrastructure;

namespace Game.Scopes
{
    public class ProjectLifetimeScope : LifetimeScope
    {
        protected override void Configure(IContainerBuilder builder)
        {
            builder.RegisterEntryPoint<TestLoader>().AsSelf();
            builder.RegisterEntryPoint<LoadingPresenter>().AsSelf();
            
            RegisterGameCoreUseCases(builder);
            
            builder.Register<IGameCoreMediator, VContainerGameCoreMediator>(Lifetime.Singleton);
        }
        
        private static void RegisterGameCoreUseCases(IContainerBuilder builder)
        {
            // Authentication
            builder.Register<AuthStateQueryHandler>(Lifetime.Transient).AsImplementedInterfaces();
            builder.Register<PlatformLoginCredentialsQueryHandler>(Lifetime.Transient).AsImplementedInterfaces();
            builder.Register<LoginCommandHandler>(Lifetime.Transient).AsImplementedInterfaces();
            builder.Register<LoadUserGameDataCommandHandler>(Lifetime.Transient).AsImplementedInterfaces();
        }
    }
}
