using Cysharp.Threading.Tasks;
using Game.Loaders.Content;
using Presenters;
using UI;
using UI.Screens;
using VContainer;

namespace Game.Presenters
{
    public class LoadingPresenter : BaseVisualPresenter
    {
        [Inject] private readonly TestLoader _testLoader;

        private UIScreenDispatcher _screenDispatcher;

        protected override void OnInitialize()
        {
            _screenDispatcher = UIDispatcher.Get<UIScreenDispatcher>();
            
            _testLoader.AllTasksCompleted += HandleAllTasksCompleted;
            _testLoader.Load().Forget();
        }

        protected override void OnDispose()
        {
            _testLoader.AllTasksCompleted -= HandleAllTasksCompleted;
        }

        private void HandleAllTasksCompleted()
        {
            _screenDispatcher.Hide();
        }
    }
}