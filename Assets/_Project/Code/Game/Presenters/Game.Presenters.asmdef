{"name": "Game.Presenters", "rootNamespace": "", "references": ["Infrastructure.Shared", "Presenters", "Unity.TextMeshPro", "UI", "UI.Screens", "Targeting", "Localization", "Content.Constants", "<PERSON><PERSON><PERSON>", "Game.Loaders", "Loading", "UniTask", "Messaging", "Game.UI", "Content", "Game", "UI.Windows", "Game.DI.Infrastructure", "Game.Core"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}