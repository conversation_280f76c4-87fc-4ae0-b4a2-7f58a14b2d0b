# Changelog for com.google.play.core

## [1.8.5] - 2024-09-18
- Removed unused dependency on Google Play Android App Bundle.

## [1.8.4] - 2024-07-05
- Bumped the Core-common Java maven dependency to 2.0.4 to benefit from a number of bug fixes
- Bumped minimum supported Unity version to 2018.4

## [1.8.3] - 2024-01-12
- Added integrity prefix constant

## [1.8.2] - 2023-03-10
- Updated Android App Bundle dependency from 1.8.0 to 1.9.0

## [1.8.1] - 2022-09-08
- Updated plugin's data collection procedure. For more information and the opt-out
  process, please refer to the [data collection](https://github.com/google/play-unity-plugins#data-collection)
  section in README.

## [1.8.0] - 2022-07-06
### New Features
- Migrated to [Core-Common 2.0.0](https://developer.android.com/reference/com/google/android/play/core/release-notes-common#2-0-0).
## [1.7.0] - 2022-02-15
### New Features
- Incremented version number to match other packages

## [1.6.1] - 2022-1-20
### New Features
 - Updated Java Play Core dependency from 1.10.2 to 1.10.3

## [1.6.0] - 2021-11-15
### New Features
 - Updated Java Play Core dependency from 1.10.0 to 1.10.2
### Other
 - Removed playcore.aar file and switched to EDM4U for dependency management

## [1.5.0] - 2021-06-14
### Other
 - Removed ability to compile plugin with Unity 5.6, 2017.1, 2017.2, 2017.3, 2018.1, and 2018.2

## [1.4.0] - 2021-03-08
### New Features
 - Updated playcore.aar from 1.8.2 to 1.10.0

## [1.3.0] - 2020-09-30
### New Features
 - Updated playcore.aar from 1.8.0 to 1.8.2
 - Updated Play Core library license to https://developer.android.com/guide/playcore/license
### Bug Fixes
 - Removed unnecessary warning

## [1.2.0] - 2020-07-27
### New Features
 - Updated playcore.aar from 1.7.3 to 1.8.0

## [1.1.1] - 2020-06-08
### New Features
 - Updated playcore.aar from 1.7.2 to 1.7.3

## [1.1.0] - 2020-05-04
### New Features
 - Updated playcore.aar from 1.7.1 to 1.7.2
 - Added Proguard config file
 - Updated documentation

## [1.0.0] - 2020-03-17
### New Features
 - Initial release
 - Includes playcore.aar version 1.7.1
